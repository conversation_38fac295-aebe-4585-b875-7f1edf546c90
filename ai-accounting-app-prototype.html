<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI记账助手 - 原型设计</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 20px;
        }
        
        .screen {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            height: 600px;
            position: relative;
        }
        
        .screen-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-weight: bold;
            font-size: 18px;
        }
        
        .screen-content {
            padding: 20px;
            height: calc(100% - 80px);
            overflow-y: auto;
        }
        
        /* 首页样式 */
        .balance-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .balance-amount {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .income-expense {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        
        .income, .expense {
            text-align: center;
        }
        
        .income-amount {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .expense-amount {
            color: #f44336;
            font-weight: bold;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        
        .action-btn {
            background: white;
            border: 2px solid #e0e0e0;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .action-btn:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        
        .action-btn i {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }
        
        .expense-btn i {
            color: #f44336;
        }
        
        .income-btn i {
            color: #4CAF50;
        }
        
        /* 记账页面样式 */
        .amount-input {
            text-align: center;
            margin: 20px 0;
        }
        
        .amount-display {
            font-size: 48px;
            font-weight: bold;
            color: #333;
            margin: 20px 0;
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        
        .category-item {
            text-align: center;
            padding: 15px;
            border-radius: 12px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .category-item:hover, .category-item.active {
            background: #667eea;
            color: white;
        }
        
        .category-item i {
            font-size: 20px;
            margin-bottom: 5px;
            display: block;
        }
        
        /* 账目列表样式 */
        .transaction-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
        
        .transaction-info {
            flex: 1;
        }
        
        .transaction-amount {
            font-weight: bold;
        }
        
        .transaction-date {
            color: #666;
            font-size: 12px;
        }
        
        /* 统计页面样式 */
        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .chart-placeholder {
            height: 200px;
            background: linear-gradient(45deg, #f0f2f5, #e1e5e9);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            margin: 15px 0;
        }
        
        /* 按钮样式 */
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        /* 输入框样式 */
        .form-group {
            margin: 15px 0;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; margin-bottom: 30px; color: #333;">
            <i class="fas fa-robot" style="color: #667eea;"></i>
            AI记账助手 - 原型设计
        </h1>
        
        <div class="prototype-grid">
            <!-- 首页/概览页 -->
            <div class="screen">
                <div class="screen-header">
                    <i class="fas fa-home"></i> 首页概览
                </div>
                <div class="screen-content">
                    <div class="balance-card">
                        <div style="font-size: 14px; opacity: 0.9;">当前余额</div>
                        <div class="balance-amount">¥12,580.50</div>
                        <div class="income-expense">
                            <div class="income">
                                <div style="font-size: 12px;">本月收入</div>
                                <div class="income-amount">+¥8,500</div>
                            </div>
                            <div class="expense">
                                <div style="font-size: 12px;">本月支出</div>
                                <div class="expense-amount">-¥3,240</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="quick-actions">
                        <div class="action-btn expense-btn">
                            <i class="fas fa-minus-circle"></i>
                            <div>记支出</div>
                        </div>
                        <div class="action-btn income-btn">
                            <i class="fas fa-plus-circle"></i>
                            <div>记收入</div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 25px;">
                        <h3 style="margin-bottom: 15px; display: flex; align-items: center;">
                            <i class="fas fa-clock" style="margin-right: 8px; color: #667eea;"></i>
                            最近记录
                        </h3>
                        <div class="transaction-item">
                            <div class="transaction-icon" style="background: #ffebee;">
                                <i class="fas fa-utensils" style="color: #f44336;"></i>
                            </div>
                            <div class="transaction-info">
                                <div>午餐</div>
                                <div class="transaction-date">今天 12:30</div>
                            </div>
                            <div class="transaction-amount" style="color: #f44336;">-¥45</div>
                        </div>
                        <div class="transaction-item">
                            <div class="transaction-icon" style="background: #e8f5e8;">
                                <i class="fas fa-wallet" style="color: #4CAF50;"></i>
                            </div>
                            <div class="transaction-info">
                                <div>工资</div>
                                <div class="transaction-date">昨天 09:00</div>
                            </div>
                            <div class="transaction-amount" style="color: #4CAF50;">+¥8,500</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 记账页面 -->
            <div class="screen">
                <div class="screen-header">
                    <i class="fas fa-edit"></i> 智能记账
                </div>
                <div class="screen-content">
                    <div class="amount-input">
                        <div style="color: #666; margin-bottom: 10px;">输入金额</div>
                        <div class="amount-display">¥0.00</div>
                    </div>

                    <div style="text-align: center; margin: 20px 0;">
                        <button style="background: #4CAF50; color: white; border: none; padding: 8px 20px; border-radius: 20px; margin: 0 5px;">收入</button>
                        <button style="background: #f44336; color: white; border: none; padding: 8px 20px; border-radius: 20px; margin: 0 5px;">支出</button>
                    </div>

                    <div style="margin: 20px 0;">
                        <h4 style="margin-bottom: 15px; color: #333;">选择分类</h4>
                        <div class="category-grid">
                            <div class="category-item active">
                                <i class="fas fa-utensils"></i>
                                <div style="font-size: 12px;">餐饮</div>
                            </div>
                            <div class="category-item">
                                <i class="fas fa-car"></i>
                                <div style="font-size: 12px;">交通</div>
                            </div>
                            <div class="category-item">
                                <i class="fas fa-shopping-cart"></i>
                                <div style="font-size: 12px;">购物</div>
                            </div>
                            <div class="category-item">
                                <i class="fas fa-home"></i>
                                <div style="font-size: 12px;">居住</div>
                            </div>
                            <div class="category-item">
                                <i class="fas fa-gamepad"></i>
                                <div style="font-size: 12px;">娱乐</div>
                            </div>
                            <div class="category-item">
                                <i class="fas fa-heartbeat"></i>
                                <div style="font-size: 12px;">医疗</div>
                            </div>
                            <div class="category-item">
                                <i class="fas fa-graduation-cap"></i>
                                <div style="font-size: 12px;">教育</div>
                            </div>
                            <div class="category-item">
                                <i class="fas fa-ellipsis-h"></i>
                                <div style="font-size: 12px;">其他</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>备注</label>
                        <input type="text" placeholder="添加备注信息...">
                    </div>

                    <div style="display: flex; gap: 10px; margin-top: 20px;">
                        <button style="flex: 1; background: #667eea; color: white; border: none; padding: 12px; border-radius: 8px;">
                            <i class="fas fa-microphone"></i> 语音记账
                        </button>
                        <button style="flex: 1; background: #764ba2; color: white; border: none; padding: 12px; border-radius: 8px;">
                            <i class="fas fa-camera"></i> 拍照记账
                        </button>
                    </div>

                    <button class="btn-primary">
                        <i class="fas fa-check"></i> 确认记账
                    </button>
                </div>
            </div>

            <!-- 账目列表页 -->
            <div class="screen">
                <div class="screen-header">
                    <i class="fas fa-list"></i> 账目明细
                </div>
                <div class="screen-content">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <select style="padding: 8px; border-radius: 6px; border: 1px solid #ddd;">
                            <option>本月</option>
                            <option>上月</option>
                            <option>本年</option>
                        </select>
                        <div style="display: flex; gap: 10px;">
                            <button style="padding: 6px 12px; background: #f0f0f0; border: none; border-radius: 4px;">全部</button>
                            <button style="padding: 6px 12px; background: #4CAF50; color: white; border: none; border-radius: 4px;">收入</button>
                            <button style="padding: 6px 12px; background: #f44336; color: white; border: none; border-radius: 4px;">支出</button>
                        </div>
                    </div>

                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: center;">
                        <div style="color: #666; font-size: 14px;">本月净收入</div>
                        <div style="font-size: 24px; font-weight: bold; color: #4CAF50;">+¥5,260</div>
                    </div>

                    <div>
                        <div style="color: #666; font-size: 14px; margin-bottom: 10px;">今天</div>
                        <div class="transaction-item">
                            <div class="transaction-icon" style="background: #ffebee;">
                                <i class="fas fa-utensils" style="color: #f44336;"></i>
                            </div>
                            <div class="transaction-info">
                                <div style="font-weight: 500;">午餐 - 麦当劳</div>
                                <div class="transaction-date">12:30</div>
                            </div>
                            <div class="transaction-amount" style="color: #f44336;">-¥45.00</div>
                        </div>
                        <div class="transaction-item">
                            <div class="transaction-icon" style="background: #ffebee;">
                                <i class="fas fa-coffee" style="color: #f44336;"></i>
                            </div>
                            <div class="transaction-info">
                                <div style="font-weight: 500;">咖啡 - 星巴克</div>
                                <div class="transaction-date">09:15</div>
                            </div>
                            <div class="transaction-amount" style="color: #f44336;">-¥32.00</div>
                        </div>

                        <div style="color: #666; font-size: 14px; margin: 20px 0 10px 0;">昨天</div>
                        <div class="transaction-item">
                            <div class="transaction-icon" style="background: #e8f5e8;">
                                <i class="fas fa-wallet" style="color: #4CAF50;"></i>
                            </div>
                            <div class="transaction-info">
                                <div style="font-weight: 500;">工资收入</div>
                                <div class="transaction-date">09:00</div>
                            </div>
                            <div class="transaction-amount" style="color: #4CAF50;">+¥8,500.00</div>
                        </div>
                        <div class="transaction-item">
                            <div class="transaction-icon" style="background: #ffebee;">
                                <i class="fas fa-shopping-cart" style="color: #f44336;"></i>
                            </div>
                            <div class="transaction-info">
                                <div style="font-weight: 500;">超市购物</div>
                                <div class="transaction-date">19:30</div>
                            </div>
                            <div class="transaction-amount" style="color: #f44336;">-¥156.80</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计分析页 -->
            <div class="screen">
                <div class="screen-header">
                    <i class="fas fa-chart-bar"></i> 统计分析
                </div>
                <div class="screen-content">
                    <div class="stats-card">
                        <h4 style="margin-bottom: 15px; display: flex; align-items: center;">
                            <i class="fas fa-calendar-month" style="margin-right: 8px; color: #667eea;"></i>
                            本月概况
                        </h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; text-align: center;">
                            <div>
                                <div style="color: #4CAF50; font-size: 20px; font-weight: bold;">¥8,500</div>
                                <div style="color: #666; font-size: 12px;">总收入</div>
                            </div>
                            <div>
                                <div style="color: #f44336; font-size: 20px; font-weight: bold;">¥3,240</div>
                                <div style="color: #666; font-size: 12px;">总支出</div>
                            </div>
                        </div>
                    </div>

                    <div class="stats-card">
                        <h4 style="margin-bottom: 10px;">支出分类占比</h4>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-pie" style="font-size: 48px;"></i>
                            <div style="margin-left: 15px;">饼图展示区域</div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 12px;">
                            <div style="display: flex; align-items: center;">
                                <div style="width: 12px; height: 12px; background: #f44336; border-radius: 50%; margin-right: 8px;"></div>
                                餐饮 35%
                            </div>
                            <div style="display: flex; align-items: center;">
                                <div style="width: 12px; height: 12px; background: #2196F3; border-radius: 50%; margin-right: 8px;"></div>
                                交通 20%
                            </div>
                            <div style="display: flex; align-items: center;">
                                <div style="width: 12px; height: 12px; background: #4CAF50; border-radius: 50%; margin-right: 8px;"></div>
                                购物 25%
                            </div>
                            <div style="display: flex; align-items: center;">
                                <div style="width: 12px; height: 12px; background: #FF9800; border-radius: 50%; margin-right: 8px;"></div>
                                其他 20%
                            </div>
                        </div>
                    </div>

                    <div class="stats-card">
                        <h4 style="margin-bottom: 10px;">收支趋势</h4>
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-line" style="font-size: 48px;"></i>
                            <div style="margin-left: 15px;">折线图展示区域</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 预算管理页 -->
            <div class="screen">
                <div class="screen-header">
                    <i class="fas fa-piggy-bank"></i> 预算管理
                </div>
                <div class="screen-content">
                    <div style="background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%); color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                        <div style="font-size: 14px; opacity: 0.9;">本月预算</div>
                        <div style="font-size: 28px; font-weight: bold; margin: 8px 0;">¥5,000</div>
                        <div style="background: rgba(255,255,255,0.2); border-radius: 10px; height: 8px; margin: 10px 0;">
                            <div style="background: white; height: 100%; width: 65%; border-radius: 10px;"></div>
                        </div>
                        <div style="font-size: 12px;">已使用 ¥3,240 (65%)</div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <h4 style="margin-bottom: 15px;">分类预算</h4>

                        <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <div style="display: flex; align-items: center;">
                                    <i class="fas fa-utensils" style="color: #f44336; margin-right: 8px;"></i>
                                    <span>餐饮</span>
                                </div>
                                <div style="font-size: 14px; color: #666;">¥1,200 / ¥1,500</div>
                            </div>
                            <div style="background: #f0f0f0; border-radius: 4px; height: 6px;">
                                <div style="background: #f44336; height: 100%; width: 80%; border-radius: 4px;"></div>
                            </div>
                        </div>

                        <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <div style="display: flex; align-items: center;">
                                    <i class="fas fa-car" style="color: #2196F3; margin-right: 8px;"></i>
                                    <span>交通</span>
                                </div>
                                <div style="font-size: 14px; color: #666;">¥650 / ¥800</div>
                            </div>
                            <div style="background: #f0f0f0; border-radius: 4px; height: 6px;">
                                <div style="background: #2196F3; height: 100%; width: 81%; border-radius: 4px;"></div>
                            </div>
                        </div>

                        <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <div style="display: flex; align-items: center;">
                                    <i class="fas fa-shopping-cart" style="color: #4CAF50; margin-right: 8px;"></i>
                                    <span>购物</span>
                                </div>
                                <div style="font-size: 14px; color: #666;">¥800 / ¥1,200</div>
                            </div>
                            <div style="background: #f0f0f0; border-radius: 4px; height: 6px;">
                                <div style="background: #4CAF50; height: 100%; width: 67%; border-radius: 4px;"></div>
                            </div>
                        </div>
                    </div>

                    <button class="btn-primary">
                        <i class="fas fa-plus"></i> 设置新预算
                    </button>
                </div>
            </div>

            <!-- 设置页面 -->
            <div class="screen">
                <div class="screen-header">
                    <i class="fas fa-cog"></i> 设置
                </div>
                <div class="screen-content">
                    <div style="margin-bottom: 30px;">
                        <div style="display: flex; align-items: center; padding: 15px 0; border-bottom: 1px solid #eee;">
                            <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 20px; margin-right: 15px;">
                                <i class="fas fa-user"></i>
                            </div>
                            <div>
                                <div style="font-weight: 500; font-size: 16px;">张三</div>
                                <div style="color: #666; font-size: 14px;"><EMAIL></div>
                            </div>
                        </div>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <div style="display: flex; align-items: center; padding: 15px 0; border-bottom: 1px solid #f0f0f0; cursor: pointer;">
                            <i class="fas fa-bell" style="color: #667eea; width: 20px; margin-right: 15px;"></i>
                            <span style="flex: 1;">消息提醒</span>
                            <i class="fas fa-chevron-right" style="color: #ccc;"></i>
                        </div>

                        <div style="display: flex; align-items: center; padding: 15px 0; border-bottom: 1px solid #f0f0f0; cursor: pointer;">
                            <i class="fas fa-shield-alt" style="color: #667eea; width: 20px; margin-right: 15px;"></i>
                            <span style="flex: 1;">隐私安全</span>
                            <i class="fas fa-chevron-right" style="color: #ccc;"></i>
                        </div>

                        <div style="display: flex; align-items: center; padding: 15px 0; border-bottom: 1px solid #f0f0f0; cursor: pointer;">
                            <i class="fas fa-download" style="color: #667eea; width: 20px; margin-right: 15px;"></i>
                            <span style="flex: 1;">数据导出</span>
                            <i class="fas fa-chevron-right" style="color: #ccc;"></i>
                        </div>

                        <div style="display: flex; align-items: center; padding: 15px 0; border-bottom: 1px solid #f0f0f0; cursor: pointer;">
                            <i class="fas fa-sync-alt" style="color: #667eea; width: 20px; margin-right: 15px;"></i>
                            <span style="flex: 1;">数据同步</span>
                            <i class="fas fa-chevron-right" style="color: #ccc;"></i>
                        </div>

                        <div style="display: flex; align-items: center; padding: 15px 0; border-bottom: 1px solid #f0f0f0; cursor: pointer;">
                            <i class="fas fa-question-circle" style="color: #667eea; width: 20px; margin-right: 15px;"></i>
                            <span style="flex: 1;">帮助与反馈</span>
                            <i class="fas fa-chevron-right" style="color: #ccc;"></i>
                        </div>

                        <div style="display: flex; align-items: center; padding: 15px 0; border-bottom: 1px solid #f0f0f0; cursor: pointer;">
                            <i class="fas fa-info-circle" style="color: #667eea; width: 20px; margin-right: 15px;"></i>
                            <span style="flex: 1;">关于我们</span>
                            <i class="fas fa-chevron-right" style="color: #ccc;"></i>
                        </div>
                    </div>

                    <button style="background: #f44336; color: white; border: none; padding: 15px 30px; border-radius: 25px; font-size: 16px; cursor: pointer; width: 100%; margin-top: 20px;">
                        <i class="fas fa-sign-out-alt"></i> 退出登录
                    </button>
                </div>
            </div>
        </div>

        <!-- 底部说明 -->
        <div style="text-align: center; margin-top: 40px; padding: 20px; background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
            <h3 style="color: #333; margin-bottom: 15px;">
                <i class="fas fa-lightbulb" style="color: #667eea; margin-right: 8px;"></i>
                AI记账助手功能特色
            </h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 20px;">
                <div style="text-align: center;">
                    <i class="fas fa-microphone" style="font-size: 24px; color: #4CAF50; margin-bottom: 8px;"></i>
                    <div style="font-weight: 500;">语音记账</div>
                    <div style="font-size: 12px; color: #666;">说话即可记录，智能识别金额和分类</div>
                </div>
                <div style="text-align: center;">
                    <i class="fas fa-camera" style="font-size: 24px; color: #2196F3; margin-bottom: 8px;"></i>
                    <div style="font-weight: 500;">拍照记账</div>
                    <div style="font-size: 12px; color: #666;">拍摄小票自动识别金额和商家</div>
                </div>
                <div style="text-align: center;">
                    <i class="fas fa-chart-line" style="font-size: 24px; color: #FF9800; margin-bottom: 8px;"></i>
                    <div style="font-weight: 500;">智能分析</div>
                    <div style="font-size: 12px; color: #666;">AI分析消费习惯，提供理财建议</div>
                </div>
                <div style="text-align: center;">
                    <i class="fas fa-bell" style="font-size: 24px; color: #f44336; margin-bottom: 8px;"></i>
                    <div style="font-weight: 500;">智能提醒</div>
                    <div style="font-size: 12px; color: #666;">预算超支提醒，账单到期提醒</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
