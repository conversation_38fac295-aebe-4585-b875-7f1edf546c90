<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>位置共享应用 - 原型界面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .phone-frame {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: #fff;
            border-top: 1px solid #e5e5e5;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }
        .nav-item.active {
            color: #007AFF;
        }
        .floating-btn {
            position: absolute;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #007AFF;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0,122,255,0.3);
        }
        .map-container {
            background: linear-gradient(45deg, #4CAF50, #8BC34A);
            height: 100%;
            position: relative;
        }
        .location-pin {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #FF5722;
            font-size: 32px;
        }
        .friend-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .online-indicator {
            width: 12px;
            height: 12px;
            background: #34C759;
            border-radius: 50%;
            border: 2px solid white;
            position: absolute;
            bottom: 2px;
            right: 2px;
        }
        .section-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(375px, 1fr));
            gap: 40px;
            padding: 40px 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        .screen-info {
            text-align: center;
            margin-bottom: 20px;
        }
        .screen-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .screen-description {
            font-size: 14px;
            color: #666;
            max-width: 300px;
            margin: 0 auto;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="bg-white py-8 shadow-sm">
        <div class="max-w-6xl mx-auto px-4">
            <h1 class="text-4xl font-bold text-center text-gray-800 mb-4">位置共享应用</h1>
            <p class="text-xl text-center text-gray-600 mb-8">全面的移动应用原型界面</p>
            <div class="flex justify-center space-x-8 text-sm text-gray-500">
                <span><i class="fas fa-mobile-alt mr-2"></i>32个界面</span>
                <span><i class="fas fa-users mr-2"></i>6个用户流程</span>
                <span><i class="fas fa-map-marker-alt mr-2"></i>实时位置</span>
                <span><i class="fas fa-shield-alt mr-2"></i>隐私控制</span>
            </div>
        </div>
    </div>

    <!-- 用户认证流程部分 -->
    <div class="py-12">
        <h2 class="section-title">用户认证流程</h2>
        <div class="prototype-grid">

            <!-- 启动页面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">1. 启动页面</div>
                    <div class="screen-description">应用启动界面，包含品牌标识和加载动画</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen bg-gradient-to-br from-blue-500 to-purple-600 flex flex-col items-center justify-center text-white">
                        <div class="text-6xl mb-4">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h1 class="text-3xl font-bold mb-2">位置共享</h1>
                        <p class="text-lg opacity-90">保持联系，确保安全</p>
                        <div class="absolute bottom-20">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 欢迎页面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">2. 欢迎页面</div>
                    <div class="screen-description">引导介绍页面，展示核心功能</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="flex flex-col items-center justify-center h-full px-8 text-center">
                            <div class="text-6xl text-blue-500 mb-8">
                                <i class="fas fa-users"></i>
                            </div>
                            <h2 class="text-2xl font-bold mb-4">安全分享您的位置</h2>
                            <p class="text-gray-600 mb-8 leading-relaxed">与朋友和家人保持联系。实时分享您的位置，完全掌控隐私设置。</p>
                            <div class="space-y-4 w-full">
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold">开始使用</button>
                                <button class="w-full border border-gray-300 text-gray-700 py-4 rounded-xl font-semibold">我已有账户</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 注册页面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">3. 注册页面</div>
                    <div class="screen-description">用户注册，支持邮箱和手机号注册</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-8">
                                <button class="text-blue-500 text-xl"><i class="fas fa-arrow-left"></i></button>
                                <h1 class="text-xl font-bold ml-4">创建账户</h1>
                            </div>
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">姓名</label>
                                    <input type="text" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="请输入您的姓名">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                                    <input type="email" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="请输入您的邮箱">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">手机号码</label>
                                    <input type="tel" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="+86 138 0013 8000">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                                    <input type="password" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="创建密码">
                                </div>
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mt-8">创建账户</button>
                                <div class="text-center">
                                    <span class="text-gray-500">或使用以下方式继续</span>
                                </div>
                                <div class="flex space-x-4">
                                    <button class="flex-1 border border-gray-300 py-3 rounded-xl flex items-center justify-center">
                                        <i class="fab fa-google text-red-500 mr-2"></i> 谷歌
                                    </button>
                                    <button class="flex-1 border border-gray-300 py-3 rounded-xl flex items-center justify-center">
                                        <i class="fab fa-apple mr-2"></i> 苹果
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 登录页面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">4. 登录页面</div>
                    <div class="screen-description">用户登录验证，支持邮箱/手机号和密码</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-8">
                                <button class="text-blue-500 text-xl"><i class="fas fa-arrow-left"></i></button>
                                <h1 class="text-xl font-bold ml-4">欢迎回来</h1>
                            </div>
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">邮箱或手机号</label>
                                    <input type="text" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="请输入邮箱或手机号">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                                    <input type="password" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="请输入密码">
                                </div>
                                <div class="flex items-center justify-between">
                                    <label class="flex items-center">
                                        <input type="checkbox" class="mr-2">
                                        <span class="text-sm text-gray-600">记住我</span>
                                    </label>
                                    <a href="#" class="text-sm text-blue-500">忘记密码？</a>
                                </div>
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mt-8">登录</button>
                                <div class="text-center">
                                    <span class="text-gray-500">还没有账户？ </span>
                                    <a href="#" class="text-blue-500 font-semibold">立即注册</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 手机验证 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">5. 手机验证</div>
                    <div class="screen-description">短信验证码输入，确保账户安全</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-8">
                                <button class="text-blue-500 text-xl"><i class="fas fa-arrow-left"></i></button>
                                <h1 class="text-xl font-bold ml-4">验证手机号</h1>
                            </div>
                            <div class="text-center mb-8">
                                <div class="text-6xl text-green-500 mb-4">
                                    <i class="fas fa-sms"></i>
                                </div>
                                <h2 class="text-xl font-bold mb-2">输入验证码</h2>
                                <p class="text-gray-600">我们已向以下号码发送6位验证码<br>+86 138 0013 8000</p>
                            </div>
                            <div class="flex justify-center space-x-3 mb-8">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                            </div>
                            <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mb-4">验证</button>
                            <div class="text-center">
                                <span class="text-gray-500">没有收到验证码？ </span>
                                <a href="#" class="text-blue-500 font-semibold">重新发送 (0:30)</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 忘记密码 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">6. 忘记密码</div>
                    <div class="screen-description">通过邮箱或短信重置密码</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-8">
                                <button class="text-blue-500 text-xl"><i class="fas fa-arrow-left"></i></button>
                                <h1 class="text-xl font-bold ml-4">重置密码</h1>
                            </div>
                            <div class="text-center mb-8">
                                <div class="text-6xl text-orange-500 mb-4">
                                    <i class="fas fa-key"></i>
                                </div>
                                <h2 class="text-xl font-bold mb-2">忘记密码了？</h2>
                                <p class="text-gray-600">输入您的邮箱或手机号，我们将发送重置链接给您</p>
                            </div>
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">邮箱或手机号</label>
                                    <input type="text" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="请输入邮箱或手机号">
                                </div>
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold">发送重置链接</button>
                                <div class="text-center">
                                    <a href="#" class="text-blue-500 font-semibold">返回登录</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- 引导流程部分 -->
    <div class="py-12 bg-gray-50">
        <h2 class="section-title">引导流程</h2>
        <div class="prototype-grid">

            <!-- 权限请求 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">7. 权限请求</div>
                    <div class="screen-description">位置和通知权限设置</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6 flex flex-col justify-center h-full">
                            <div class="text-center mb-8">
                                <div class="text-6xl text-blue-500 mb-4">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <h2 class="text-2xl font-bold mb-4">开启位置访问</h2>
                                <p class="text-gray-600 mb-8">我们需要访问您的位置信息，以便与您信任的联系人分享</p>
                            </div>
                            <div class="space-y-4 mb-8">
                                <div class="flex items-center p-4 bg-blue-50 rounded-xl">
                                    <div class="text-blue-500 text-2xl mr-4">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold">隐私保护</h3>
                                        <p class="text-sm text-gray-600">您可以控制谁能看到您的位置</p>
                                    </div>
                                </div>
                                <div class="flex items-center p-4 bg-green-50 rounded-xl">
                                    <div class="text-green-500 text-2xl mr-4">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold">时间限制</h3>
                                        <p class="text-sm text-gray-600">设置分享时长限制</p>
                                    </div>
                                </div>
                            </div>
                            <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mb-4">允许位置访问</button>
                            <button class="w-full border border-gray-300 text-gray-700 py-4 rounded-xl font-semibold">稍后再说</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人资料设置 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">8. 个人资料设置</div>
                    <div class="screen-description">创建用户资料，包含头像和基本信息</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-8">
                                <button class="text-blue-500 text-xl"><i class="fas fa-arrow-left"></i></button>
                                <h1 class="text-xl font-bold ml-4">设置个人资料</h1>
                            </div>
                            <div class="text-center mb-8">
                                <div class="relative inline-block">
                                    <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center text-gray-400 text-3xl">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <button class="absolute bottom-0 right-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white">
                                        <i class="fas fa-camera text-sm"></i>
                                    </button>
                                </div>
                                <p class="text-sm text-gray-600 mt-2">添加头像</p>
                            </div>
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">显示名称</label>
                                    <input type="text" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="朋友们如何找到您？">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">状态消息</label>
                                    <input type="text" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="分享您的心情">
                                </div>
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                                    <div>
                                        <h3 class="font-semibold">允许他人发现我的资料</h3>
                                        <p class="text-sm text-gray-600">朋友可以通过手机号找到您</p>
                                    </div>
                                    <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                        <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                    </div>
                                </div>
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mt-8">继续</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 教程演示 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">9. 教程演示</div>
                    <div class="screen-description">交互式教程，展示应用核心功能</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6 flex flex-col justify-center h-full">
                            <div class="text-center mb-8">
                                <div class="text-6xl text-green-500 mb-4">
                                    <i class="fas fa-users"></i>
                                </div>
                                <h2 class="text-2xl font-bold mb-4">与朋友分享</h2>
                                <p class="text-gray-600 mb-8">点击分享按钮，即可与选定的朋友分享您的位置，并设置分享时长</p>
                            </div>
                            <div class="flex justify-center mb-8">
                                <div class="flex space-x-2">
                                    <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                    <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                                    <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                                </div>
                            </div>
                            <div class="flex space-x-4">
                                <button class="flex-1 border border-gray-300 text-gray-700 py-4 rounded-xl font-semibold">跳过</button>
                                <button class="flex-1 bg-blue-500 text-white py-4 rounded-xl font-semibold">下一步</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 位置访问设置 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">10. 位置访问设置</div>
                    <div class="screen-description">最终位置权限和精度设置</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6 flex flex-col justify-center h-full">
                            <div class="text-center mb-8">
                                <div class="text-6xl text-red-500 mb-4">
                                    <i class="fas fa-crosshairs"></i>
                                </div>
                                <h2 class="text-2xl font-bold mb-4">位置精度</h2>
                                <p class="text-gray-600 mb-8">选择您偏好的位置分享精度级别</p>
                            </div>
                            <div class="space-y-4 mb-8">
                                <div class="p-4 border-2 border-blue-500 bg-blue-50 rounded-xl">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="font-semibold text-blue-700">精确位置</h3>
                                            <p class="text-sm text-blue-600">准确的GPS坐标</p>
                                        </div>
                                        <div class="w-5 h-5 border-2 border-blue-500 rounded-full bg-blue-500 flex items-center justify-center">
                                            <div class="w-2 h-2 bg-white rounded-full"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 border border-gray-300 rounded-xl">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="font-semibold">大概位置</h3>
                                            <p class="text-sm text-gray-600">大致区域（约1公里范围）</p>
                                        </div>
                                        <div class="w-5 h-5 border-2 border-gray-300 rounded-full"></div>
                                    </div>
                                </div>
                            </div>
                            <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mb-4">使用精确位置继续</button>
                            <p class="text-xs text-gray-500 text-center">您可以随时在设置中更改此选项</p>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- 主导航界面部分 -->
    <div class="py-12">
        <h2 class="section-title">主导航界面</h2>
        <div class="prototype-grid">

            <!-- 地图视图（主页） -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">11. 地图视图（主页）</div>
                    <div class="screen-description">主界面，包含交互式地图和实时位置</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="relative h-full">
                            <!-- 顶部栏 -->
                            <div class="absolute top-0 left-0 right-0 z-10 bg-white/90 backdrop-blur-sm p-4 flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="friend-avatar mr-3">张三</div>
                                    <div>
                                        <h2 class="font-bold">张三</h2>
                                        <p class="text-sm text-gray-600">正在与3位朋友分享</p>
                                    </div>
                                </div>
                                <div class="flex space-x-3">
                                    <button class="w-10 h-10 bg-white rounded-full shadow-md flex items-center justify-center">
                                        <i class="fas fa-search text-gray-600"></i>
                                    </button>
                                    <button class="w-10 h-10 bg-white rounded-full shadow-md flex items-center justify-center">
                                        <i class="fas fa-bell text-gray-600"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Map Container -->
                            <div class="map-container">
                                <!-- Location pins -->
                                <div class="location-pin">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="absolute top-1/3 left-1/4 text-blue-500 text-2xl">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="absolute bottom-1/3 right-1/4 text-green-500 text-2xl">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                            </div>

                            <!-- Map Controls -->
                            <div class="absolute right-4 top-1/2 transform -translate-y-1/2 space-y-2">
                                <button class="w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center">
                                    <i class="fas fa-plus text-gray-600"></i>
                                </button>
                                <button class="w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center">
                                    <i class="fas fa-minus text-gray-600"></i>
                                </button>
                                <button class="w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center">
                                    <i class="fas fa-crosshairs text-blue-500"></i>
                                </button>
                            </div>

                            <!-- Floating Action Button -->
                            <div class="floating-btn">
                                <i class="fas fa-share-alt"></i>
                            </div>

                            <!-- 底部导航 -->
                            <div class="bottom-nav">
                                <div class="nav-item active">
                                    <i class="fas fa-map text-xl"></i>
                                    <span class="text-xs">地图</span>
                                </div>
                                <div class="nav-item">
                                    <i class="fas fa-users text-xl"></i>
                                    <span class="text-xs">朋友</span>
                                </div>
                                <div class="nav-item">
                                    <i class="fas fa-comments text-xl"></i>
                                    <span class="text-xs">消息</span>
                                </div>
                                <div class="nav-item">
                                    <i class="fas fa-user text-xl"></i>
                                    <span class="text-xs">我的</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 朋友列表 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">12. 朋友列表</div>
                    <div class="screen-description">联系人管理，显示在线状态和位置分享</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-4">
                            <!-- 顶部栏 -->
                            <div class="flex items-center justify-between mb-6">
                                <h1 class="text-2xl font-bold">朋友</h1>
                                <button class="text-blue-500 text-xl">
                                    <i class="fas fa-user-plus"></i>
                                </button>
                            </div>

                            <!-- 搜索栏 -->
                            <div class="relative mb-6">
                                <input type="text" class="w-full bg-gray-100 rounded-xl px-4 py-3 pl-10" placeholder="搜索朋友">
                                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>

                            <!-- 朋友列表 -->
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <div class="friend-avatar">小红</div>
                                            <div class="online-indicator"></div>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="font-semibold">小红</h3>
                                            <p class="text-sm text-gray-600">正在分享位置 • 2分钟前</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-map-marker-alt text-blue-500 text-sm"></i>
                                        </button>
                                        <button class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-comment text-green-500 text-sm"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <div class="friend-avatar">小明</div>
                                            <div class="online-indicator"></div>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="font-semibold">小明</h3>
                                            <p class="text-sm text-gray-600">在线 • 5分钟前活跃</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-map-marker-alt text-gray-400 text-sm"></i>
                                        </button>
                                        <button class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-comment text-green-500 text-sm"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <div class="friend-avatar">小李</div>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="font-semibold">小李</h3>
                                            <p class="text-sm text-gray-600">离线 • 2小时前活跃</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-map-marker-alt text-gray-400 text-sm"></i>
                                        </button>
                                        <button class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-comment text-green-500 text-sm"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 底部导航 -->
                        <div class="bottom-nav">
                            <div class="nav-item">
                                <i class="fas fa-map text-xl"></i>
                                <span class="text-xs">地图</span>
                            </div>
                            <div class="nav-item active">
                                <i class="fas fa-users text-xl"></i>
                                <span class="text-xs">朋友</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-comments text-xl"></i>
                                <span class="text-xs">消息</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-user text-xl"></i>
                                <span class="text-xs">我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分享位置弹窗 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">13. 分享位置弹窗</div>
                    <div class="screen-description">快速位置分享，包含联系人选择和时长设置</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen bg-black/50 flex items-end">
                        <div class="w-full bg-white rounded-t-3xl p-6">
                            <div class="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-6"></div>
                            <h2 class="text-xl font-bold mb-6">分享您的位置</h2>

                            <!-- 联系人选择 -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-3">选择朋友</h3>
                                <div class="flex space-x-3 mb-4">
                                    <div class="text-center">
                                        <div class="relative">
                                            <div class="friend-avatar mb-2">小红</div>
                                            <div class="absolute -top-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                                                <i class="fas fa-check text-white text-xs"></i>
                                            </div>
                                        </div>
                                        <span class="text-xs">小红</span>
                                    </div>
                                    <div class="text-center">
                                        <div class="friend-avatar mb-2">小明</div>
                                        <span class="text-xs">小明</span>
                                    </div>
                                    <div class="text-center">
                                        <div class="friend-avatar mb-2">小李</div>
                                        <span class="text-xs">小李</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 时长选择 -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-3">分享时长</h3>
                                <div class="grid grid-cols-3 gap-3">
                                    <button class="p-3 border-2 border-blue-500 bg-blue-50 rounded-xl text-center">
                                        <div class="font-semibold text-blue-700">15分钟</div>
                                    </button>
                                    <button class="p-3 border border-gray-300 rounded-xl text-center">
                                        <div class="font-semibold">1小时</div>
                                    </button>
                                    <button class="p-3 border border-gray-300 rounded-xl text-center">
                                        <div class="font-semibold">8小时</div>
                                    </button>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="space-y-3">
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold">分享位置</button>
                                <button class="w-full border border-gray-300 text-gray-700 py-4 rounded-xl font-semibold">取消</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人设置 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">14. 个人设置</div>
                    <div class="screen-description">用户资料管理和应用设置</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-4">
                            <!-- 顶部栏 -->
                            <div class="flex items-center justify-between mb-6">
                                <h1 class="text-2xl font-bold">我的</h1>
                                <button class="text-blue-500">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>

                            <!-- 个人信息 -->
                            <div class="text-center mb-8">
                                <div class="relative inline-block mb-4">
                                    <div class="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                                        张三
                                    </div>
                                    <div class="online-indicator"></div>
                                </div>
                                <h2 class="text-xl font-bold">张三</h2>
                                <p class="text-gray-600">享受美好生活 🌟</p>
                                <div class="flex justify-center space-x-6 mt-4 text-sm">
                                    <div class="text-center">
                                        <div class="font-bold text-lg">12</div>
                                        <div class="text-gray-600">朋友</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="font-bold text-lg">48</div>
                                        <div class="text-gray-600">位置</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="font-bold text-lg">156</div>
                                        <div class="text-gray-600">签到</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 设置菜单 -->
                            <div class="space-y-2">
                                <div class="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-shield-alt text-blue-500"></i>
                                        </div>
                                        <span class="font-semibold">隐私与安全</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-bell text-green-500"></i>
                                        </div>
                                        <span class="font-semibold">通知设置</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-cog text-orange-500"></i>
                                        </div>
                                        <span class="font-semibold">应用设置</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-question-circle text-purple-500"></i>
                                        </div>
                                        <span class="font-semibold">帮助与支持</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 底部导航 -->
                        <div class="bottom-nav">
                            <div class="nav-item">
                                <i class="fas fa-map text-xl"></i>
                                <span class="text-xs">地图</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-users text-xl"></i>
                                <span class="text-xs">朋友</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-comments text-xl"></i>
                                <span class="text-xs">消息</span>
                            </div>
                            <div class="nav-item active">
                                <i class="fas fa-user text-xl"></i>
                                <span class="text-xs">我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- 消息聊天功能部分 -->
    <div class="py-12 bg-gray-50">
        <h2 class="section-title">消息聊天功能</h2>
        <div class="prototype-grid">

            <!-- 消息列表界面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">15. 消息列表</div>
                    <div class="screen-description">显示与朋友的聊天记录和位置分享消息</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-4">
                            <!-- 顶部栏 -->
                            <div class="flex items-center justify-between mb-6">
                                <h1 class="text-2xl font-bold">消息</h1>
                                <button class="text-blue-500 text-xl">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>

                            <!-- 搜索栏 -->
                            <div class="relative mb-6">
                                <input type="text" class="w-full bg-gray-100 rounded-xl px-4 py-3 pl-10" placeholder="搜索消息">
                                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>

                            <!-- 消息列表 -->
                            <div class="space-y-3">
                                <div class="flex items-center p-3 bg-white rounded-xl shadow-sm">
                                    <div class="relative">
                                        <div class="friend-avatar mr-3">小红</div>
                                        <div class="online-indicator"></div>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between mb-1">
                                            <h3 class="font-semibold">小红</h3>
                                            <span class="text-xs text-gray-500">2分钟前</span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-map-marker-alt text-blue-500 text-sm mr-2"></i>
                                            <p class="text-sm text-gray-600">分享了位置信息</p>
                                        </div>
                                    </div>
                                    <div class="w-2 h-2 bg-blue-500 rounded-full ml-2"></div>
                                </div>

                                <div class="flex items-center p-3 bg-white rounded-xl shadow-sm">
                                    <div class="relative">
                                        <div class="friend-avatar mr-3">小明</div>
                                        <div class="online-indicator"></div>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between mb-1">
                                            <h3 class="font-semibold">小明</h3>
                                            <span class="text-xs text-gray-500">10分钟前</span>
                                        </div>
                                        <p class="text-sm text-gray-600">我已经到了，你在哪里？</p>
                                    </div>
                                </div>

                                <div class="flex items-center p-3 bg-white rounded-xl shadow-sm">
                                    <div class="relative">
                                        <div class="friend-avatar mr-3 bg-gradient-to-br from-purple-500 to-pink-500">家庭</div>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between mb-1">
                                            <h3 class="font-semibold">家庭群聊</h3>
                                            <span class="text-xs text-gray-500">1小时前</span>
                                        </div>
                                        <p class="text-sm text-gray-600">妈妈: 大家都平安到家了吗？</p>
                                    </div>
                                    <div class="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center ml-2">
                                        <span class="text-xs text-white font-bold">3</span>
                                    </div>
                                </div>

                                <div class="flex items-center p-3 bg-white rounded-xl shadow-sm">
                                    <div class="relative">
                                        <div class="friend-avatar mr-3">小李</div>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between mb-1">
                                            <h3 class="font-semibold">小李</h3>
                                            <span class="text-xs text-gray-500">昨天</span>
                                        </div>
                                        <p class="text-sm text-gray-600">谢谢你分享的位置信息</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 底部导航 -->
                        <div class="bottom-nav">
                            <div class="nav-item">
                                <i class="fas fa-map text-xl"></i>
                                <span class="text-xs">地图</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-users text-xl"></i>
                                <span class="text-xs">朋友</span>
                            </div>
                            <div class="nav-item active">
                                <i class="fas fa-comments text-xl"></i>
                                <span class="text-xs">消息</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-user text-xl"></i>
                                <span class="text-xs">我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 单个聊天对话界面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">16. 聊天对话</div>
                    <div class="screen-description">与朋友的单独聊天界面，包含位置分享消息</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="flex flex-col h-full">
                            <!-- 聊天头部 -->
                            <div class="p-4 bg-white border-b border-gray-200 flex items-center">
                                <button class="text-blue-500 text-xl mr-4">
                                    <i class="fas fa-arrow-left"></i>
                                </button>
                                <div class="relative">
                                    <div class="friend-avatar mr-3">小红</div>
                                    <div class="online-indicator"></div>
                                </div>
                                <div class="flex-1">
                                    <h3 class="font-semibold">小红</h3>
                                    <p class="text-sm text-green-500">在线</p>
                                </div>
                                <div class="flex space-x-3">
                                    <button class="text-gray-600">
                                        <i class="fas fa-phone"></i>
                                    </button>
                                    <button class="text-gray-600">
                                        <i class="fas fa-video"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- 聊天内容 -->
                            <div class="flex-1 p-4 space-y-4 overflow-y-auto">
                                <!-- 接收的消息 -->
                                <div class="flex items-start">
                                    <div class="friend-avatar mr-3 text-sm">小红</div>
                                    <div class="bg-gray-100 rounded-2xl rounded-tl-md px-4 py-2 max-w-xs">
                                        <p class="text-sm">嗨！你现在在哪里？</p>
                                        <span class="text-xs text-gray-500">10:30</span>
                                    </div>
                                </div>

                                <!-- 发送的消息 -->
                                <div class="flex items-start justify-end">
                                    <div class="bg-blue-500 text-white rounded-2xl rounded-tr-md px-4 py-2 max-w-xs">
                                        <p class="text-sm">我在咖啡厅，马上分享位置给你</p>
                                        <span class="text-xs text-blue-200">10:32</span>
                                    </div>
                                </div>

                                <!-- 位置分享消息 -->
                                <div class="flex items-start justify-end">
                                    <div class="bg-blue-500 text-white rounded-2xl rounded-tr-md p-3 max-w-xs">
                                        <div class="flex items-center mb-2">
                                            <i class="fas fa-map-marker-alt mr-2"></i>
                                            <span class="text-sm font-semibold">当前位置</span>
                                        </div>
                                        <div class="bg-white/20 rounded-lg p-2 mb-2">
                                            <div class="w-full h-20 bg-green-400 rounded flex items-center justify-center">
                                                <i class="fas fa-map text-white text-2xl"></i>
                                            </div>
                                        </div>
                                        <p class="text-xs">星巴克咖啡（中山路店）</p>
                                        <p class="text-xs text-blue-200">分享15分钟 • 10:33</p>
                                    </div>
                                </div>

                                <!-- 接收的消息 -->
                                <div class="flex items-start">
                                    <div class="friend-avatar mr-3 text-sm">小红</div>
                                    <div class="bg-gray-100 rounded-2xl rounded-tl-md px-4 py-2 max-w-xs">
                                        <p class="text-sm">好的，我马上过来！</p>
                                        <span class="text-xs text-gray-500">10:35</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 输入栏 -->
                            <div class="p-4 bg-white border-t border-gray-200">
                                <div class="flex items-center space-x-3">
                                    <button class="text-gray-500">
                                        <i class="fas fa-plus-circle text-xl"></i>
                                    </button>
                                    <div class="flex-1 bg-gray-100 rounded-full px-4 py-2 flex items-center">
                                        <input type="text" placeholder="输入消息..." class="flex-1 bg-transparent outline-none">
                                        <button class="text-gray-500 ml-2">
                                            <i class="fas fa-smile"></i>
                                        </button>
                                    </div>
                                    <button class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-map-marker-alt text-white"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 群聊界面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">17. 群聊界面</div>
                    <div class="screen-description">多人位置分享群组聊天界面</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="flex flex-col h-full">
                            <!-- 群聊头部 -->
                            <div class="p-4 bg-white border-b border-gray-200 flex items-center">
                                <button class="text-blue-500 text-xl mr-4">
                                    <i class="fas fa-arrow-left"></i>
                                </button>
                                <div class="friend-avatar mr-3 bg-gradient-to-br from-purple-500 to-pink-500">家庭</div>
                                <div class="flex-1">
                                    <h3 class="font-semibold">家庭群聊</h3>
                                    <p class="text-sm text-gray-500">4名成员</p>
                                </div>
                                <div class="flex space-x-3">
                                    <button class="text-gray-600">
                                        <i class="fas fa-map-marked-alt"></i>
                                    </button>
                                    <button class="text-gray-600">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- 位置分享状态栏 -->
                            <div class="p-3 bg-blue-50 border-b border-blue-100">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="fas fa-users text-blue-500 mr-2"></i>
                                        <span class="text-sm font-semibold text-blue-700">3人正在分享位置</span>
                                    </div>
                                    <button class="text-blue-500 text-sm font-semibold">查看地图</button>
                                </div>
                            </div>

                            <!-- 群聊内容 -->
                            <div class="flex-1 p-4 space-y-4 overflow-y-auto">
                                <!-- 系统消息 -->
                                <div class="text-center">
                                    <div class="inline-block bg-gray-200 rounded-full px-3 py-1">
                                        <span class="text-xs text-gray-600">今天</span>
                                    </div>
                                </div>

                                <!-- 妈妈的消息 -->
                                <div class="flex items-start">
                                    <div class="friend-avatar mr-3 text-xs bg-gradient-to-br from-pink-400 to-red-400">妈妈</div>
                                    <div class="flex-1">
                                        <div class="flex items-center mb-1">
                                            <span class="text-sm font-semibold text-gray-700 mr-2">妈妈</span>
                                            <span class="text-xs text-gray-500">14:20</span>
                                        </div>
                                        <div class="bg-gray-100 rounded-2xl rounded-tl-md px-4 py-2 max-w-xs">
                                            <p class="text-sm">大家都平安到家了吗？</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 爸爸分享位置 -->
                                <div class="flex items-start">
                                    <div class="friend-avatar mr-3 text-xs bg-gradient-to-br from-blue-400 to-indigo-400">爸爸</div>
                                    <div class="flex-1">
                                        <div class="flex items-center mb-1">
                                            <span class="text-sm font-semibold text-gray-700 mr-2">爸爸</span>
                                            <span class="text-xs text-gray-500">14:25</span>
                                        </div>
                                        <div class="bg-gray-100 rounded-2xl rounded-tl-md p-3 max-w-xs">
                                            <div class="flex items-center mb-2">
                                                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                                                <span class="text-sm font-semibold">我的位置</span>
                                            </div>
                                            <div class="bg-green-400 rounded-lg h-16 flex items-center justify-center mb-2">
                                                <i class="fas fa-map text-white text-xl"></i>
                                            </div>
                                            <p class="text-xs text-gray-600">还在公司加班</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 我的消息 -->
                                <div class="flex items-start justify-end">
                                    <div class="bg-blue-500 text-white rounded-2xl rounded-tr-md px-4 py-2 max-w-xs">
                                        <p class="text-sm">我已经到家了，很安全</p>
                                        <span class="text-xs text-blue-200">14:30</span>
                                    </div>
                                </div>

                                <!-- 弟弟的消息 -->
                                <div class="flex items-start">
                                    <div class="friend-avatar mr-3 text-xs bg-gradient-to-br from-green-400 to-teal-400">弟弟</div>
                                    <div class="flex-1">
                                        <div class="flex items-center mb-1">
                                            <span class="text-sm font-semibold text-gray-700 mr-2">弟弟</span>
                                            <span class="text-xs text-gray-500">14:35</span>
                                        </div>
                                        <div class="bg-gray-100 rounded-2xl rounded-tl-md px-4 py-2 max-w-xs">
                                            <p class="text-sm">我也到了，大家都安全！</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 输入栏 -->
                            <div class="p-4 bg-white border-t border-gray-200">
                                <div class="flex items-center space-x-3">
                                    <button class="text-gray-500">
                                        <i class="fas fa-plus-circle text-xl"></i>
                                    </button>
                                    <div class="flex-1 bg-gray-100 rounded-full px-4 py-2 flex items-center">
                                        <input type="text" placeholder="发送消息到群聊..." class="flex-1 bg-transparent outline-none">
                                        <button class="text-gray-500 ml-2">
                                            <i class="fas fa-smile"></i>
                                        </button>
                                    </div>
                                    <button class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-share-alt text-white"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- 位置历史与管理功能部分 -->
    <div class="py-12">
        <h2 class="section-title">位置历史与管理</h2>
        <div class="prototype-grid">

            <!-- 位置历史记录界面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">18. 位置历史</div>
                    <div class="screen-description">查看和管理位置分享历史记录</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-4">
                            <!-- 顶部栏 -->
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <button class="text-blue-500 text-xl mr-4">
                                        <i class="fas fa-arrow-left"></i>
                                    </button>
                                    <h1 class="text-xl font-bold">位置历史</h1>
                                </div>
                                <button class="text-blue-500">
                                    <i class="fas fa-filter"></i>
                                </button>
                            </div>

                            <!-- 时间筛选 -->
                            <div class="flex space-x-2 mb-6">
                                <button class="px-4 py-2 bg-blue-500 text-white rounded-full text-sm font-semibold">今天</button>
                                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-semibold">本周</button>
                                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-semibold">本月</button>
                            </div>

                            <!-- 历史记录列表 -->
                            <div class="space-y-4">
                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-start justify-between mb-3">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-share-alt text-blue-500"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold">分享给小红</h3>
                                                <p class="text-sm text-gray-600">今天 14:30 - 14:45</p>
                                            </div>
                                        </div>
                                        <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">已完成</span>
                                    </div>
                                    <div class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-map-marker-alt mr-2"></i>
                                        <span>星巴克咖啡（中山路店）</span>
                                    </div>
                                    <div class="flex items-center justify-between mt-3">
                                        <span class="text-xs text-gray-500">分享时长: 15分钟</span>
                                        <button class="text-blue-500 text-sm">查看详情</button>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-start justify-between mb-3">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-users text-purple-500"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold">家庭群聊分享</h3>
                                                <p class="text-sm text-gray-600">今天 12:00 - 20:00</p>
                                            </div>
                                        </div>
                                        <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">进行中</span>
                                    </div>
                                    <div class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-home mr-2"></i>
                                        <span>家 → 公司 → 家</span>
                                    </div>
                                    <div class="flex items-center justify-between mt-3">
                                        <span class="text-xs text-gray-500">已分享: 6小时32分钟</span>
                                        <button class="text-red-500 text-sm">停止分享</button>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-start justify-between mb-3">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-clock text-green-500"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold">临时分享给小明</h3>
                                                <p class="text-sm text-gray-600">昨天 19:15 - 20:15</p>
                                            </div>
                                        </div>
                                        <span class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full">已过期</span>
                                    </div>
                                    <div class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-utensils mr-2"></i>
                                        <span>海底捞火锅店</span>
                                    </div>
                                    <div class="flex items-center justify-between mt-3">
                                        <span class="text-xs text-gray-500">分享时长: 1小时</span>
                                        <button class="text-blue-500 text-sm">重新分享</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 位置收藏管理界面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">19. 位置收藏</div>
                    <div class="screen-description">管理收藏的位置和常用地点</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-4">
                            <!-- 顶部栏 -->
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <button class="text-blue-500 text-xl mr-4">
                                        <i class="fas fa-arrow-left"></i>
                                    </button>
                                    <h1 class="text-xl font-bold">位置收藏</h1>
                                </div>
                                <button class="text-blue-500">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>

                            <!-- 快速添加 -->
                            <div class="bg-blue-50 rounded-xl p-4 mb-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="fas fa-map-marker-alt text-blue-500 text-xl mr-3"></i>
                                        <div>
                                            <h3 class="font-semibold text-blue-700">添加当前位置</h3>
                                            <p class="text-sm text-blue-600">将当前位置保存为收藏</p>
                                        </div>
                                    </div>
                                    <button class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-semibold">添加</button>
                                </div>
                            </div>

                            <!-- 收藏分类 -->
                            <div class="flex space-x-2 mb-6">
                                <button class="px-4 py-2 bg-blue-500 text-white rounded-full text-sm font-semibold">全部</button>
                                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-semibold">家</button>
                                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-semibold">工作</button>
                                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm font-semibold">娱乐</button>
                            </div>

                            <!-- 收藏列表 -->
                            <div class="space-y-3">
                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-home text-red-500"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold">家</h3>
                                                <p class="text-sm text-gray-600">北京市朝阳区xxx小区</p>
                                                <div class="flex items-center mt-1">
                                                    <span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full mr-2">家</span>
                                                    <span class="text-xs text-gray-500">使用 156 次</span>
                                                </div>
                                            </div>
                                        </div>
                                        <button class="text-gray-400">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-building text-blue-500"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold">公司</h3>
                                                <p class="text-sm text-gray-600">北京市海淀区中关村软件园</p>
                                                <div class="flex items-center mt-1">
                                                    <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full mr-2">工作</span>
                                                    <span class="text-xs text-gray-500">使用 89 次</span>
                                                </div>
                                            </div>
                                        </div>
                                        <button class="text-gray-400">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-coffee text-green-500"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold">星巴克咖啡</h3>
                                                <p class="text-sm text-gray-600">中山路店</p>
                                                <div class="flex items-center mt-1">
                                                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full mr-2">娱乐</span>
                                                    <span class="text-xs text-gray-500">使用 23 次</span>
                                                </div>
                                            </div>
                                        </div>
                                        <button class="text-gray-400">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-dumbbell text-purple-500"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold">健身房</h3>
                                                <p class="text-sm text-gray-600">24小时健身中心</p>
                                                <div class="flex items-center mt-1">
                                                    <span class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full mr-2">娱乐</span>
                                                    <span class="text-xs text-gray-500">使用 45 次</span>
                                                </div>
                                            </div>
                                        </div>
                                        <button class="text-gray-400">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 地理围栏设置界面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">20. 地理围栏</div>
                    <div class="screen-description">设置位置提醒和自动分享区域</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-4">
                            <!-- 顶部栏 -->
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <button class="text-blue-500 text-xl mr-4">
                                        <i class="fas fa-arrow-left"></i>
                                    </button>
                                    <h1 class="text-xl font-bold">地理围栏</h1>
                                </div>
                                <button class="text-blue-500">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>

                            <!-- 功能说明 -->
                            <div class="bg-blue-50 rounded-xl p-4 mb-6">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-blue-500 text-xl mr-3 mt-1"></i>
                                    <div>
                                        <h3 class="font-semibold text-blue-700 mb-1">什么是地理围栏？</h3>
                                        <p class="text-sm text-blue-600">当您进入或离开指定区域时，自动触发位置分享或发送通知给指定联系人。</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 围栏列表 -->
                            <div class="space-y-4">
                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="flex items-center">
                                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-home text-green-500"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold">家庭安全围栏</h3>
                                                <p class="text-sm text-gray-600">半径: 500米</p>
                                            </div>
                                        </div>
                                        <div class="w-12 h-6 bg-green-500 rounded-full relative">
                                            <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                        </div>
                                    </div>
                                    <div class="space-y-2">
                                        <div class="flex items-center text-sm">
                                            <i class="fas fa-sign-in-alt text-green-500 mr-2"></i>
                                            <span class="text-gray-600">进入时: 通知家庭群聊</span>
                                        </div>
                                        <div class="flex items-center text-sm">
                                            <i class="fas fa-sign-out-alt text-orange-500 mr-2"></i>
                                            <span class="text-gray-600">离开时: 自动分享位置30分钟</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between mt-3">
                                        <span class="text-xs text-gray-500">今日触发 3 次</span>
                                        <button class="text-blue-500 text-sm">编辑</button>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="flex items-center">
                                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-building text-blue-500"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold">工作区域提醒</h3>
                                                <p class="text-sm text-gray-600">半径: 200米</p>
                                            </div>
                                        </div>
                                        <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                            <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                        </div>
                                    </div>
                                    <div class="space-y-2">
                                        <div class="flex items-center text-sm">
                                            <i class="fas fa-sign-in-alt text-green-500 mr-2"></i>
                                            <span class="text-gray-600">进入时: 通知小红我已到公司</span>
                                        </div>
                                        <div class="flex items-center text-sm">
                                            <i class="fas fa-sign-out-alt text-orange-500 mr-2"></i>
                                            <span class="text-gray-600">离开时: 通知下班了</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between mt-3">
                                        <span class="text-xs text-gray-500">本周触发 12 次</span>
                                        <button class="text-blue-500 text-sm">编辑</button>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-4 opacity-60">
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="flex items-center">
                                            <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-school text-gray-400"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold text-gray-500">学校接送提醒</h3>
                                                <p class="text-sm text-gray-400">半径: 300米</p>
                                            </div>
                                        </div>
                                        <div class="w-12 h-6 bg-gray-300 rounded-full relative">
                                            <div class="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5"></div>
                                        </div>
                                    </div>
                                    <div class="space-y-2">
                                        <div class="flex items-center text-sm">
                                            <i class="fas fa-sign-in-alt text-gray-400 mr-2"></i>
                                            <span class="text-gray-400">进入时: 通知已到学校</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between mt-3">
                                        <span class="text-xs text-gray-400">已暂停</span>
                                        <button class="text-blue-500 text-sm">编辑</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- 安全与紧急功能部分 -->
    <div class="py-12 bg-gray-50">
        <h2 class="section-title">安全与紧急功能</h2>
        <div class="prototype-grid">

            <!-- 紧急联系人设置 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">21. 紧急联系人</div>
                    <div class="screen-description">设置和管理紧急情况下的联系人</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-4">
                            <!-- 顶部栏 -->
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <button class="text-blue-500 text-xl mr-4">
                                        <i class="fas fa-arrow-left"></i>
                                    </button>
                                    <h1 class="text-xl font-bold">紧急联系人</h1>
                                </div>
                                <button class="text-blue-500">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>

                            <!-- 功能说明 -->
                            <div class="bg-red-50 rounded-xl p-4 mb-6">
                                <div class="flex items-start">
                                    <i class="fas fa-exclamation-triangle text-red-500 text-xl mr-3 mt-1"></i>
                                    <div>
                                        <h3 class="font-semibold text-red-700 mb-1">紧急情况自动通知</h3>
                                        <p class="text-sm text-red-600">当您触发SOS求助时，系统会自动向这些联系人发送您的位置信息和求助消息。</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 紧急联系人列表 -->
                            <div class="space-y-4">
                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-heart text-red-500"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold">妈妈</h3>
                                                <p class="text-sm text-gray-600">+86 138 0000 0001</p>
                                                <div class="flex items-center mt-1">
                                                    <span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full mr-2">主要联系人</span>
                                                    <span class="text-xs text-green-600">✓ 已验证</span>
                                                </div>
                                            </div>
                                        </div>
                                        <button class="text-gray-400">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-user-tie text-blue-500"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold">爸爸</h3>
                                                <p class="text-sm text-gray-600">+86 138 0000 0002</p>
                                                <div class="flex items-center mt-1">
                                                    <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full mr-2">家庭成员</span>
                                                    <span class="text-xs text-green-600">✓ 已验证</span>
                                                </div>
                                            </div>
                                        </div>
                                        <button class="text-gray-400">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-user-friends text-green-500"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold">小红</h3>
                                                <p class="text-sm text-gray-600">+86 138 0000 0003</p>
                                                <div class="flex items-center mt-1">
                                                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full mr-2">好友</span>
                                                    <span class="text-xs text-green-600">✓ 已验证</span>
                                                </div>
                                            </div>
                                        </div>
                                        <button class="text-gray-400">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- 添加新联系人 -->
                                <div class="bg-white rounded-xl shadow-sm p-4 border-2 border-dashed border-gray-200">
                                    <div class="flex items-center justify-center">
                                        <div class="text-center">
                                            <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                                <i class="fas fa-plus text-gray-400"></i>
                                            </div>
                                            <h3 class="font-semibold text-gray-600">添加紧急联系人</h3>
                                            <p class="text-sm text-gray-500">最多可添加5个联系人</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 设置选项 -->
                            <div class="mt-8 space-y-4">
                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="font-semibold">自动发送位置</h3>
                                            <p class="text-sm text-gray-600">SOS时自动发送实时位置</p>
                                        </div>
                                        <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                            <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="font-semibold">语音留言</h3>
                                            <p class="text-sm text-gray-600">同时发送预录制的语音消息</p>
                                        </div>
                                        <div class="w-12 h-6 bg-gray-300 rounded-full relative">
                                            <div class="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- SOS紧急求助界面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">22. SOS紧急求助</div>
                    <div class="screen-description">紧急情况下的快速求助界面</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen bg-red-500">
                        <div class="status-bar text-white">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="flex flex-col items-center justify-center h-full text-white p-6">
                            <!-- SOS标识 -->
                            <div class="text-8xl mb-8 animate-pulse">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>

                            <h1 class="text-3xl font-bold mb-4">紧急求助</h1>
                            <p class="text-xl text-center mb-8 opacity-90">正在向紧急联系人发送求助信息...</p>

                            <!-- 倒计时 -->
                            <div class="relative mb-8">
                                <div class="w-32 h-32 border-8 border-white/30 rounded-full flex items-center justify-center">
                                    <div class="text-center">
                                        <div class="text-4xl font-bold">5</div>
                                        <div class="text-sm">秒后发送</div>
                                    </div>
                                </div>
                                <div class="absolute inset-0 border-8 border-white border-r-transparent rounded-full animate-spin"></div>
                            </div>

                            <!-- 状态信息 -->
                            <div class="space-y-3 mb-8 text-center">
                                <div class="flex items-center justify-center">
                                    <i class="fas fa-check-circle mr-2"></i>
                                    <span>位置信息已获取</span>
                                </div>
                                <div class="flex items-center justify-center">
                                    <i class="fas fa-users mr-2"></i>
                                    <span>将通知 3 位紧急联系人</span>
                                </div>
                                <div class="flex items-center justify-center">
                                    <i class="fas fa-phone mr-2"></i>
                                    <span>准备拨打紧急电话</span>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="space-y-4 w-full">
                                <button class="w-full bg-white text-red-500 py-4 rounded-xl font-bold text-lg">
                                    取消求助
                                </button>
                                <button class="w-full border-2 border-white text-white py-4 rounded-xl font-bold text-lg">
                                    立即拨打 110
                                </button>
                            </div>

                            <!-- 底部提示 -->
                            <p class="text-sm text-center mt-6 opacity-75">
                                长按电源键3秒可快速触发SOS
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 安全检查提醒界面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">23. 安全检查</div>
                    <div class="screen-description">定时安全检查和提醒功能</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-4">
                            <!-- 顶部栏 -->
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <button class="text-blue-500 text-xl mr-4">
                                        <i class="fas fa-arrow-left"></i>
                                    </button>
                                    <h1 class="text-xl font-bold">安全检查</h1>
                                </div>
                                <button class="text-blue-500">
                                    <i class="fas fa-cog"></i>
                                </button>
                            </div>

                            <!-- 当前状态 -->
                            <div class="bg-green-50 rounded-xl p-4 mb-6">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mr-4">
                                        <i class="fas fa-shield-alt text-white"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-green-700">安全状态良好</h3>
                                        <p class="text-sm text-green-600">最后检查: 2小时前</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 安全检查设置 -->
                            <div class="space-y-4">
                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <div>
                                            <h3 class="font-semibold">定时安全检查</h3>
                                            <p class="text-sm text-gray-600">定期确认您的安全状态</p>
                                        </div>
                                        <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                            <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                        </div>
                                    </div>
                                    <div class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-clock mr-2"></i>
                                        <span>每4小时检查一次</span>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <div>
                                            <h3 class="font-semibold">夜间模式</h3>
                                            <p class="text-sm text-gray-600">22:00-06:00 暂停检查</p>
                                        </div>
                                        <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                            <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <div>
                                            <h3 class="font-semibold">位置异常检测</h3>
                                            <p class="text-sm text-gray-600">检测到异常位置时提醒</p>
                                        </div>
                                        <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                            <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 检查历史 -->
                            <div class="mt-8">
                                <h3 class="font-semibold mb-4">最近检查记录</h3>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                            <div>
                                                <p class="font-semibold text-sm">安全确认</p>
                                                <p class="text-xs text-gray-600">今天 14:30</p>
                                            </div>
                                        </div>
                                        <span class="text-xs text-green-600">正常</span>
                                    </div>

                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-check-circle text-green-500 mr-3"></i>
                                            <div>
                                                <p class="font-semibold text-sm">安全确认</p>
                                                <p class="text-xs text-gray-600">今天 10:30</p>
                                            </div>
                                        </div>
                                        <span class="text-xs text-green-600">正常</span>
                                    </div>

                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-exclamation-triangle text-orange-500 mr-3"></i>
                                            <div>
                                                <p class="font-semibold text-sm">未响应提醒</p>
                                                <p class="text-xs text-gray-600">昨天 18:30</p>
                                            </div>
                                        </div>
                                        <span class="text-xs text-orange-600">延迟</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- 高级设置功能部分 -->
    <div class="py-12">
        <h2 class="section-title">高级设置功能</h2>
        <div class="prototype-grid">

            <!-- 详细隐私设置界面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">24. 隐私设置</div>
                    <div class="screen-description">详细的隐私控制和权限管理</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-4">
                            <!-- 顶部栏 -->
                            <div class="flex items-center mb-6">
                                <button class="text-blue-500 text-xl mr-4">
                                    <i class="fas fa-arrow-left"></i>
                                </button>
                                <h1 class="text-xl font-bold">隐私与安全</h1>
                            </div>

                            <!-- 位置隐私 -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-4 text-gray-700">位置隐私</h3>
                                <div class="space-y-3">
                                    <div class="bg-white rounded-xl shadow-sm p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">位置精度</h4>
                                                <p class="text-sm text-gray-600">控制分享的位置精确度</p>
                                            </div>
                                            <div class="text-blue-500 text-sm font-semibold">精确</div>
                                        </div>
                                    </div>

                                    <div class="bg-white rounded-xl shadow-sm p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">隐身模式</h4>
                                                <p class="text-sm text-gray-600">对所有人隐藏在线状态</p>
                                            </div>
                                            <div class="w-12 h-6 bg-gray-300 rounded-full relative">
                                                <div class="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-white rounded-xl shadow-sm p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">位置历史</h4>
                                                <p class="text-sm text-gray-600">保存位置分享历史记录</p>
                                            </div>
                                            <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                                <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 联系人权限 -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-4 text-gray-700">联系人权限</h3>
                                <div class="space-y-3">
                                    <div class="bg-white rounded-xl shadow-sm p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">谁可以找到我</h4>
                                                <p class="text-sm text-gray-600">通过手机号搜索到我</p>
                                            </div>
                                            <div class="text-blue-500 text-sm font-semibold">朋友</div>
                                        </div>
                                    </div>

                                    <div class="bg-white rounded-xl shadow-sm p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">谁可以向我发送位置请求</h4>
                                                <p class="text-sm text-gray-600">请求查看我的位置</p>
                                            </div>
                                            <div class="text-blue-500 text-sm font-semibold">所有人</div>
                                        </div>
                                    </div>

                                    <div class="bg-white rounded-xl shadow-sm p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">自动接受分享请求</h4>
                                                <p class="text-sm text-gray-600">来自朋友的位置分享请求</p>
                                            </div>
                                            <div class="w-12 h-6 bg-gray-300 rounded-full relative">
                                                <div class="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 数据管理 -->
                            <div>
                                <h3 class="font-semibold mb-4 text-gray-700">数据管理</h3>
                                <div class="space-y-3">
                                    <button class="w-full bg-white rounded-xl shadow-sm p-4 text-left">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">下载我的数据</h4>
                                                <p class="text-sm text-gray-600">导出所有位置和聊天数据</p>
                                            </div>
                                            <i class="fas fa-download text-blue-500"></i>
                                        </div>
                                    </button>

                                    <button class="w-full bg-white rounded-xl shadow-sm p-4 text-left">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold text-red-600">清除位置历史</h4>
                                                <p class="text-sm text-gray-600">删除所有位置分享记录</p>
                                            </div>
                                            <i class="fas fa-trash text-red-500"></i>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 通知管理详细设置 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">25. 通知管理</div>
                    <div class="screen-description">详细的通知类型和提醒设置</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-4">
                            <!-- 顶部栏 -->
                            <div class="flex items-center mb-6">
                                <button class="text-blue-500 text-xl mr-4">
                                    <i class="fas fa-arrow-left"></i>
                                </button>
                                <h1 class="text-xl font-bold">通知设置</h1>
                            </div>

                            <!-- 推送通知 -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-4 text-gray-700">推送通知</h3>
                                <div class="space-y-3">
                                    <div class="bg-white rounded-xl shadow-sm p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">位置分享请求</h4>
                                                <p class="text-sm text-gray-600">朋友请求查看您的位置</p>
                                            </div>
                                            <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                                <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-white rounded-xl shadow-sm p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">朋友到达提醒</h4>
                                                <p class="text-sm text-gray-600">朋友到达指定位置时通知</p>
                                            </div>
                                            <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                                <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-white rounded-xl shadow-sm p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">安全检查提醒</h4>
                                                <p class="text-sm text-gray-600">定时安全状态确认</p>
                                            </div>
                                            <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                                <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-white rounded-xl shadow-sm p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">新消息通知</h4>
                                                <p class="text-sm text-gray-600">聊天消息和位置分享</p>
                                            </div>
                                            <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                                <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 通知时间 -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-4 text-gray-700">通知时间</h3>
                                <div class="space-y-3">
                                    <div class="bg-white rounded-xl shadow-sm p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">免打扰模式</h4>
                                                <p class="text-sm text-gray-600">22:00 - 08:00</p>
                                            </div>
                                            <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                                <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-white rounded-xl shadow-sm p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">紧急通知例外</h4>
                                                <p class="text-sm text-gray-600">SOS求助无视免打扰</p>
                                            </div>
                                            <div class="w-12 h-6 bg-red-500 rounded-full relative">
                                                <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 通知样式 -->
                            <div>
                                <h3 class="font-semibold mb-4 text-gray-700">通知样式</h3>
                                <div class="space-y-3">
                                    <div class="bg-white rounded-xl shadow-sm p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">声音提醒</h4>
                                                <p class="text-sm text-gray-600">默认通知铃声</p>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="text-blue-500 text-sm mr-2">默认</span>
                                                <i class="fas fa-chevron-right text-gray-400"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-white rounded-xl shadow-sm p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">震动提醒</h4>
                                                <p class="text-sm text-gray-600">接收通知时震动</p>
                                            </div>
                                            <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                                <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-white rounded-xl shadow-sm p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">锁屏显示</h4>
                                                <p class="text-sm text-gray-600">在锁屏上显示通知内容</p>
                                            </div>
                                            <div class="w-12 h-6 bg-gray-300 rounded-full relative">
                                                <div class="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 账户安全设置界面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">26. 账户安全</div>
                    <div class="screen-description">账户安全管理和登录设备控制</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-4">
                            <!-- 顶部栏 -->
                            <div class="flex items-center mb-6">
                                <button class="text-blue-500 text-xl mr-4">
                                    <i class="fas fa-arrow-left"></i>
                                </button>
                                <h1 class="text-xl font-bold">账户安全</h1>
                            </div>

                            <!-- 安全状态 -->
                            <div class="bg-green-50 rounded-xl p-4 mb-6">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mr-4">
                                        <i class="fas fa-shield-check text-white"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-green-700">账户安全良好</h3>
                                        <p class="text-sm text-green-600">所有安全设置已启用</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 登录安全 -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-4 text-gray-700">登录安全</h3>
                                <div class="space-y-3">
                                    <div class="bg-white rounded-xl shadow-sm p-4">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">双重验证</h4>
                                                <p class="text-sm text-gray-600">短信验证码登录保护</p>
                                            </div>
                                            <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                                <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <button class="w-full bg-white rounded-xl shadow-sm p-4 text-left">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">修改密码</h4>
                                                <p class="text-sm text-gray-600">上次修改: 3个月前</p>
                                            </div>
                                            <i class="fas fa-chevron-right text-gray-400"></i>
                                        </div>
                                    </button>

                                    <button class="w-full bg-white rounded-xl shadow-sm p-4 text-left">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">登录设备管理</h4>
                                                <p class="text-sm text-gray-600">查看和管理已登录设备</p>
                                            </div>
                                            <i class="fas fa-chevron-right text-gray-400"></i>
                                        </div>
                                    </button>
                                </div>
                            </div>

                            <!-- 账户操作 -->
                            <div>
                                <h3 class="font-semibold mb-4 text-gray-700">账户操作</h3>
                                <div class="space-y-3">
                                    <button class="w-full bg-white rounded-xl shadow-sm p-4 text-left">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold">注销账户</h4>
                                                <p class="text-sm text-gray-600">退出当前设备登录</p>
                                            </div>
                                            <i class="fas fa-sign-out-alt text-orange-500"></i>
                                        </div>
                                    </button>

                                    <button class="w-full bg-white rounded-xl shadow-sm p-4 text-left">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h4 class="font-semibold text-red-600">删除账户</h4>
                                                <p class="text-sm text-gray-600">永久删除账户和所有数据</p>
                                            </div>
                                            <i class="fas fa-trash text-red-500"></i>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- 页脚 -->
    <div class="bg-gray-800 text-white py-12">
        <div class="max-w-6xl mx-auto px-4 text-center">
            <h3 class="text-2xl font-bold mb-4">位置共享应用原型</h3>
            <p class="text-gray-300 mb-6">完整的移动应用原型，包含26个界面，涵盖所有核心功能和高级特性</p>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-6">
                <div class="flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>用户认证流程</span>
                </div>
                <div class="flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>实时位置分享</span>
                </div>
                <div class="flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>消息聊天功能</span>
                </div>
                <div class="flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>位置历史管理</span>
                </div>
                <div class="flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>安全紧急功能</span>
                </div>
                <div class="flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>隐私控制</span>
                </div>
                <div class="flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>地理围栏</span>
                </div>
                <div class="flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>高级设置</span>
                </div>
            </div>
            <div class="text-sm text-gray-400">
                <p>开发就绪 • 移动端优先响应式设计 • 生产级UI组件 • 完整功能覆盖</p>
            </div>
        </div>
    </div>
</body>
</html>
